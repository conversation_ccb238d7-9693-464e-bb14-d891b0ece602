// =============================================================================
// MAIN JAVASCRIPT - RIO Middle East Trading LLC
// =============================================================================

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all modules
    Navigation.init();
    Forms.init();
    Modals.init();
    LanguageSwitcher.init();
    ToastNotifications.init();
    PartnerFilter.init();
    StatsCounter.init();
    
    // Initialize focus-visible polyfill for better accessibility
    if (typeof window.applyFocusVisiblePolyfill === 'function') {
        window.applyFocusVisiblePolyfill(document);
    }
});

// =============================================================================
// NAVIGATION MODULE
// =============================================================================
const Navigation = {
    init() {
        this.handleScrollBehavior();
        this.handleMobileMenu();
        this.handleDropdowns();
    },
    
    handleScrollBehavior() {
        const navbar = document.getElementById('mainNavbar');
        if (!navbar) return;
        
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add scrolled class for styling
            if (scrollTop > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
            
            lastScrollTop = scrollTop;
        });
    },
    
    handleMobileMenu() {
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');
        
        if (!navbarToggler || !navbarCollapse) return;
        
        // Close mobile menu when clicking on nav links
        const navLinks = navbarCollapse.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                if (navbarCollapse.classList.contains('show')) {
                    navbarToggler.click();
                }
            });
        });
    },
    
    handleDropdowns() {
        // Handle dropdown accessibility
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggle.click();
                }
            });
        });
    }
};

// =============================================================================
// FORMS MODULE
// =============================================================================
const Forms = {
    init() {
        this.handleFormValidation();
        this.handleFileUploads();
    },
    
    handleFormValidation() {
        const forms = document.querySelectorAll('form[novalidate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                if (this.validateForm(form)) {
                    this.submitForm(form);
                }
                
                form.classList.add('was-validated');
            });
        });
    },
    
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else {
                field.classList.remove('is-invalid');
                field.classList.add('is-valid');
            }
            
            // Email validation
            if (field.type === 'email' && field.value.trim()) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(field.value)) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                    field.classList.add('is-valid');
                }
            }
        });
        
        return isValid;
    },
    
    submitForm(form) {
        const formData = new FormData(form);
        const formType = form.id;
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Sending...';
        submitBtn.disabled = true;
        
        // Simulate form submission (replace with actual API call)
        setTimeout(() => {
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            
            // Show success message
            ToastNotifications.show('success', 'Message sent successfully! We\'ll respond within 1-2 business days.');
            
            // Reset form
            form.reset();
            form.classList.remove('was-validated');
            
            // Close modal if form is in modal
            const modal = form.closest('.modal');
            if (modal) {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        }, 2000);
    },
    
    handleFileUploads() {
        const fileInputs = document.querySelectorAll('input[type="file"]');
        
        fileInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    // Validate file size (5MB limit)
                    if (file.size > 5 * 1024 * 1024) {
                        ToastNotifications.show('error', 'File size must be less than 5MB');
                        input.value = '';
                        return;
                    }
                    
                    // Validate file type
                    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain', 'image/jpeg', 'image/png'];
                    if (!allowedTypes.includes(file.type)) {
                        ToastNotifications.show('error', 'Invalid file type. Please upload PDF, DOC, DOCX, TXT, JPG, or PNG files.');
                        input.value = '';
                        return;
                    }
                }
            });
        });
    }
};

// =============================================================================
// MODALS MODULE
// =============================================================================
const Modals = {
    init() {
        this.handleModalEvents();
        this.handlePartnerModal();
        this.handleContactModal();
    },
    
    handleModalEvents() {
        // Handle modal focus management
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            modal.addEventListener('shown.bs.modal', () => {
                const firstInput = modal.querySelector('input, textarea, select');
                if (firstInput) {
                    firstInput.focus();
                }
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                // Clear form validation states
                const form = modal.querySelector('form');
                if (form) {
                    form.classList.remove('was-validated');
                    const fields = form.querySelectorAll('.is-valid, .is-invalid');
                    fields.forEach(field => {
                        field.classList.remove('is-valid', 'is-invalid');
                    });
                }
            });
        });
    },
    
    handlePartnerModal() {
        const partnerCards = document.querySelectorAll('[data-partner]');
        const partnerModal = document.getElementById('partnerModal');
        const partnerModalBody = document.getElementById('partnerModalBody');
        
        if (!partnerModal || !partnerModalBody) return;
        
        partnerCards.forEach(card => {
            card.addEventListener('click', () => {
                const partnerId = card.dataset.partner;
                this.loadPartnerDetails(partnerId, partnerModalBody);
            });
        });
    },
    
    loadPartnerDetails(partnerId, container) {
        // Partner data (in real implementation, this would come from an API)
        const partnerData = {
            socomore: {
                name: 'SOCOMORE',
                description: 'Leading manufacturer of aerospace maintenance chemicals and surface treatments.',
                products: ['Aircraft cleaning chemicals', 'Surface treatments', 'Corrosion inhibitors', 'Paint strippers'],
                website: 'https://www.socomore.com',
                logo: 'assets/img/partners/socomore-logo.png'
            },
            laselec: {
                name: 'LASELEC',
                description: 'Specialist in electrical and electronic solutions for aerospace and industrial applications.',
                products: ['Electrical components', 'Electronic systems', 'Testing equipment', 'Maintenance tools'],
                website: 'https://www.laselec.com',
                logo: 'assets/img/partners/laselec-logo.png'
            },
            nyco: {
                name: 'NYCO',
                description: 'Global leader in aerospace lubricants and specialty chemicals.',
                products: ['Aviation lubricants', 'Hydraulic fluids', 'Greases', 'Specialty chemicals'],
                website: 'https://www.nyco.com',
                logo: 'assets/img/partners/nyco-logo.png'
            },
            cantek: {
                name: 'CANTEK',
                description: 'Innovative cooling and industrial solutions provider.',
                products: ['Cooling systems', 'Industrial equipment', 'HVAC solutions', 'Maintenance services'],
                website: 'https://www.cantek.com',
                logo: 'assets/img/partners/cantek-logo.png'
            }
        };
        
        const partner = partnerData[partnerId] || {
            name: 'Partner',
            description: 'Trusted partner providing quality solutions.',
            products: ['Quality products', 'Professional services', 'Technical support'],
            website: '#',
            logo: ''
        };
        
        container.innerHTML = `
            <div class="row align-items-center mb-4">
                <div class="col-md-3">
                    ${partner.logo ? `<img src="${partner.logo}" alt="${partner.name}" class="img-fluid">` : `<h4>${partner.name}</h4>`}
                </div>
                <div class="col-md-9">
                    <h4>${partner.name}</h4>
                    <p>${partner.description}</p>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <h5>Key Products & Services:</h5>
                    <ul class="list-unstyled">
                        ${partner.products.map(product => `<li><i class="bi bi-check-circle text-success me-2"></i>${product}</li>`).join('')}
                    </ul>
                    ${partner.website !== '#' ? `<a href="${partner.website}" target="_blank" rel="noopener" class="btn btn-outline-primary">Visit Website <i class="bi bi-arrow-up-right ms-1"></i></a>` : ''}
                </div>
            </div>
        `;
    },
    
    handleContactModal() {
        const contactButtons = document.querySelectorAll('[data-bs-target="#contactModal"]');
        const contactDivisionField = document.getElementById('contactDivision');
        
        if (!contactDivisionField) return;
        
        contactButtons.forEach(button => {
            button.addEventListener('click', () => {
                const division = button.dataset.division;
                if (division) {
                    contactDivisionField.value = division.charAt(0).toUpperCase() + division.slice(1);
                }
            });
        });
    }
};

// =============================================================================
// LANGUAGE SWITCHER MODULE
// =============================================================================
const LanguageSwitcher = {
    init() {
        this.handleLanguageSwitch();
        this.loadSavedLanguage();
    },

    handleLanguageSwitch() {
        const languageLinks = document.querySelectorAll('[data-lang]');

        languageLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = link.dataset.lang;
                this.switchLanguage(lang);
            });
        });
    },

    switchLanguage(lang) {
        // Save language preference
        localStorage.setItem('preferred-language', lang);

        // Update document attributes
        document.documentElement.lang = lang;
        document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';

        // Update active language in dropdown
        const languageLinks = document.querySelectorAll('[data-lang]');
        languageLinks.forEach(link => {
            link.classList.remove('active');
            if (link.dataset.lang === lang) {
                link.classList.add('active');
            }
        });

        // Update dropdown button text
        const dropdownButtons = document.querySelectorAll('#languageDropdown');
        dropdownButtons.forEach(button => {
            button.innerHTML = `<i class="bi bi-globe me-1"></i>${lang.toUpperCase()}`;
        });

        // Add RTL class to body if Arabic
        if (lang === 'ar') {
            document.body.classList.add('rtl');
        } else {
            document.body.classList.remove('rtl');
        }

        ToastNotifications.show('info', `Language switched to ${lang === 'ar' ? 'Arabic' : 'English'}`);
    },

    loadSavedLanguage() {
        const savedLang = localStorage.getItem('preferred-language');
        if (savedLang) {
            this.switchLanguage(savedLang);
        }
    }
};

// =============================================================================
// TOAST NOTIFICATIONS MODULE
// =============================================================================
const ToastNotifications = {
    init() {
        this.createToastContainer();
    },

    createToastContainer() {
        if (!document.getElementById('toastContainer')) {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(container);
        }
    },

    show(type, message, duration = 5000) {
        const container = document.getElementById('toastContainer');
        if (!container) return;

        const toastId = 'toast-' + Date.now();
        const iconMap = {
            success: 'bi-check-circle-fill',
            error: 'bi-exclamation-triangle-fill',
            warning: 'bi-exclamation-triangle-fill',
            info: 'bi-info-circle-fill'
        };

        const colorMap = {
            success: 'text-success',
            error: 'text-danger',
            warning: 'text-warning',
            info: 'text-primary'
        };

        const toastHTML = `
            <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi ${iconMap[type]} ${colorMap[type]} me-2"></i>
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', toastHTML);

        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: duration });

        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
};

// =============================================================================
// PARTNER FILTER MODULE
// =============================================================================
const PartnerFilter = {
    init() {
        this.handleFilterButtons();
    },

    handleFilterButtons() {
        const filterButtons = document.querySelectorAll('[data-filter]');
        const partnerCards = document.querySelectorAll('[data-category]');

        if (!filterButtons.length || !partnerCards.length) return;

        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filter = button.dataset.filter;

                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Filter cards
                partnerCards.forEach(card => {
                    const categories = card.dataset.category.split(' ');

                    if (filter === 'all' || categories.includes(filter)) {
                        card.style.display = 'flex';
                        card.classList.add('fade-in');
                    } else {
                        card.style.display = 'none';
                        card.classList.remove('fade-in');
                    }
                });
            });
        });
    }
};

// =============================================================================
// STATS COUNTER MODULE
// =============================================================================
const StatsCounter = {
    init() {
        this.handleStatsAnimation();
    },

    handleStatsAnimation() {
        const statsNumbers = document.querySelectorAll('[data-count]');
        if (!statsNumbers.length) return;

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statsNumbers.forEach(stat => observer.observe(stat));
    },

    animateCounter(element) {
        const target = parseInt(element.dataset.count);
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, 16);
    }
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================
const Utils = {
    // Debounce function for performance optimization
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Smooth scroll to element
    scrollTo(target, offset = 0) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;

        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    },

    // Check if element is in viewport
    isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
};

// =============================================================================
// ERROR HANDLING
// =============================================================================
window.addEventListener('error', (e) => {
    console.error('JavaScript Error:', e.error);
    // In production, you might want to send this to an error tracking service
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled Promise Rejection:', e.reason);
    // In production, you might want to send this to an error tracking service
});

// =============================================================================
// PERFORMANCE MONITORING
// =============================================================================
if ('performance' in window) {
    window.addEventListener('load', () => {
        setTimeout(() => {
            const perfData = performance.getEntriesByType('navigation')[0];
            console.log('Page Load Time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
        }, 0);
    });
}
