// =============================================================================
// GSAP ANIMATIONS - RIO Middle East Trading LLC
// =============================================================================

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// Animation configuration
const ANIMATION_CONFIG = {
    duration: {
        fast: 0.3,
        base: 0.6,
        slow: 1.0,
        slower: 1.5
    },
    easing: {
        base: "power2.out",
        bounce: "back.out(1.7)",
        elastic: "elastic.out(1, 0.3)"
    },
    stagger: {
        fast: 0.1,
        base: 0.15,
        slow: 0.2
    }
};

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Set initial states
    setInitialStates();
    
    // Initialize animations
    initHeroAnimations();
    initScrollAnimations();
    initCardAnimations();
    initStatsAnimations();
    initNavigationAnimations();
    initModalAnimations();
    initPageTransitions();
});

// =============================================================================
// INITIAL STATES
// =============================================================================
function setInitialStates() {
    // Hero elements
    gsap.set(".hero__title", { y: 50, opacity: 0 });
    gsap.set(".hero__subtitle", { y: 30, opacity: 0 });
    gsap.set(".hero__actions .btn", { y: 20, opacity: 0 });
    gsap.set(".hero__scroll", { y: 20, opacity: 0 });
    
    // Cards and sections
    gsap.set(".card--feature", { y: 30, opacity: 0 });
    gsap.set(".why-rio__item", { y: 30, opacity: 0 });
    gsap.set(".trust-strip__logo", { scale: 0.8, opacity: 0 });
    gsap.set(".stats__item", { y: 20, opacity: 0 });
    
    // Division content
    gsap.set(".division-content__grid .card", { y: 30, opacity: 0 });
    gsap.set(".partners-grid .card", { scale: 0.9, opacity: 0 });
}

// =============================================================================
// HERO ANIMATIONS
// =============================================================================
function initHeroAnimations() {
    const heroTimeline = gsap.timeline({ delay: 0.5 });
    
    heroTimeline
        .to(".hero__title", {
            y: 0,
            opacity: 1,
            duration: ANIMATION_CONFIG.duration.base,
            ease: ANIMATION_CONFIG.easing.base
        })
        .to(".hero__subtitle", {
            y: 0,
            opacity: 1,
            duration: ANIMATION_CONFIG.duration.base,
            ease: ANIMATION_CONFIG.easing.base
        }, "-=0.3")
        .to(".hero__actions .btn", {
            y: 0,
            opacity: 1,
            duration: ANIMATION_CONFIG.duration.base,
            ease: ANIMATION_CONFIG.easing.base,
            stagger: ANIMATION_CONFIG.stagger.base
        }, "-=0.2")
        .to(".hero__scroll", {
            y: 0,
            opacity: 1,
            duration: ANIMATION_CONFIG.duration.base,
            ease: ANIMATION_CONFIG.easing.base
        }, "-=0.1");
    
    // Parallax effect for hero background
    gsap.to(".hero__background", {
        yPercent: -50,
        ease: "none",
        scrollTrigger: {
            trigger: ".hero",
            start: "top bottom",
            end: "bottom top",
            scrub: true
        }
    });
}

// =============================================================================
// SCROLL ANIMATIONS
// =============================================================================
function initScrollAnimations() {
    // Trust strip logos
    gsap.to(".trust-strip__logo", {
        scale: 1,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        stagger: ANIMATION_CONFIG.stagger.base,
        scrollTrigger: {
            trigger: ".trust-strip",
            start: "top 80%",
            toggleActions: "play none none reverse"
        }
    });
    
    // Feature cards
    gsap.to(".card--feature", {
        y: 0,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        stagger: ANIMATION_CONFIG.stagger.base,
        scrollTrigger: {
            trigger: "#divisions",
            start: "top 70%",
            toggleActions: "play none none reverse"
        }
    });
    
    // Why RIO items
    gsap.to(".why-rio__item", {
        y: 0,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        stagger: ANIMATION_CONFIG.stagger.base,
        scrollTrigger: {
            trigger: "#why-rio",
            start: "top 70%",
            toggleActions: "play none none reverse"
        }
    });
    
    // Stats animation
    gsap.to(".stats__item", {
        y: 0,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.bounce,
        stagger: ANIMATION_CONFIG.stagger.base,
        scrollTrigger: {
            trigger: ".stats",
            start: "top 70%",
            toggleActions: "play none none reverse"
        }
    });
}

// =============================================================================
// CARD ANIMATIONS
// =============================================================================
function initCardAnimations() {
    // Division cards
    gsap.to(".division-content__grid .card", {
        y: 0,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        stagger: ANIMATION_CONFIG.stagger.base,
        scrollTrigger: {
            trigger: ".division-content__grid",
            start: "top 70%",
            toggleActions: "play none none reverse"
        }
    });
    
    // Partner cards
    gsap.to(".partners-grid .card", {
        scale: 1,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        stagger: ANIMATION_CONFIG.stagger.fast,
        scrollTrigger: {
            trigger: ".partners-grid",
            start: "top 70%",
            toggleActions: "play none none reverse"
        }
    });
    
    // Card hover animations
    const cards = document.querySelectorAll('.card--hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                y: -10,
                scale: 1.02,
                duration: ANIMATION_CONFIG.duration.fast,
                ease: ANIMATION_CONFIG.easing.base
            });
        });
        
        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                y: 0,
                scale: 1,
                duration: ANIMATION_CONFIG.duration.fast,
                ease: ANIMATION_CONFIG.easing.base
            });
        });
    });
}

// =============================================================================
// STATS COUNTER ANIMATIONS
// =============================================================================
function initStatsAnimations() {
    const statsNumbers = document.querySelectorAll('[data-count]');
    
    statsNumbers.forEach(stat => {
        const target = parseInt(stat.dataset.count);
        
        ScrollTrigger.create({
            trigger: stat,
            start: "top 80%",
            onEnter: () => {
                gsap.to(stat, {
                    innerHTML: target,
                    duration: ANIMATION_CONFIG.duration.slower,
                    ease: "power2.out",
                    snap: { innerHTML: 1 },
                    onUpdate: function() {
                        stat.innerHTML = Math.ceil(stat.innerHTML);
                    }
                });
            }
        });
    });
}

// =============================================================================
// NAVIGATION ANIMATIONS
// =============================================================================
function initNavigationAnimations() {
    // Navbar scroll behavior
    let lastScrollTop = 0;
    const navbar = document.getElementById('mainNavbar');
    
    if (navbar) {
        ScrollTrigger.create({
            start: 0,
            end: "max",
            onUpdate: (self) => {
                const scrollTop = self.scroll();
                
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // Scrolling down
                    gsap.to(navbar, {
                        y: -100,
                        duration: ANIMATION_CONFIG.duration.fast,
                        ease: ANIMATION_CONFIG.easing.base
                    });
                } else {
                    // Scrolling up
                    gsap.to(navbar, {
                        y: 0,
                        duration: ANIMATION_CONFIG.duration.fast,
                        ease: ANIMATION_CONFIG.easing.base
                    });
                }
                
                lastScrollTop = scrollTop;
            }
        });
    }
    
    // Smooth scroll for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const target = document.querySelector(link.getAttribute('href'));
            if (target) {
                gsap.to(window, {
                    duration: ANIMATION_CONFIG.duration.base,
                    scrollTo: {
                        y: target,
                        offsetY: 100
                    },
                    ease: ANIMATION_CONFIG.easing.base
                });
            }
        });
    });
}

// =============================================================================
// MODAL ANIMATIONS
// =============================================================================
function initModalAnimations() {
    const modals = document.querySelectorAll('.modal');
    
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', () => {
            gsap.set(modal.querySelector('.modal-content'), {
                scale: 0.8,
                opacity: 0
            });
        });
        
        modal.addEventListener('shown.bs.modal', () => {
            gsap.to(modal.querySelector('.modal-content'), {
                scale: 1,
                opacity: 1,
                duration: ANIMATION_CONFIG.duration.fast,
                ease: ANIMATION_CONFIG.easing.bounce
            });
        });
        
        modal.addEventListener('hide.bs.modal', () => {
            gsap.to(modal.querySelector('.modal-content'), {
                scale: 0.8,
                opacity: 0,
                duration: ANIMATION_CONFIG.duration.fast,
                ease: ANIMATION_CONFIG.easing.base
            });
        });
    });
}

// =============================================================================
// PAGE TRANSITIONS
// =============================================================================
function initPageTransitions() {
    // Fade in page content
    gsap.to("main", {
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        delay: 0.2
    });
    
    // Animate breadcrumbs
    gsap.to(".breadcrumb-item", {
        x: 0,
        opacity: 1,
        duration: ANIMATION_CONFIG.duration.base,
        ease: ANIMATION_CONFIG.easing.base,
        stagger: ANIMATION_CONFIG.stagger.fast,
        delay: 0.3
    });
}

// =============================================================================
// UTILITY ANIMATIONS
// =============================================================================
const AnimationUtils = {
    // Fade in element
    fadeIn(element, duration = ANIMATION_CONFIG.duration.base) {
        gsap.to(element, {
            opacity: 1,
            duration: duration,
            ease: ANIMATION_CONFIG.easing.base
        });
    },
    
    // Fade out element
    fadeOut(element, duration = ANIMATION_CONFIG.duration.base) {
        gsap.to(element, {
            opacity: 0,
            duration: duration,
            ease: ANIMATION_CONFIG.easing.base
        });
    },
    
    // Slide up element
    slideUp(element, duration = ANIMATION_CONFIG.duration.base) {
        gsap.to(element, {
            y: 0,
            opacity: 1,
            duration: duration,
            ease: ANIMATION_CONFIG.easing.base
        });
    },
    
    // Scale in element
    scaleIn(element, duration = ANIMATION_CONFIG.duration.base) {
        gsap.to(element, {
            scale: 1,
            opacity: 1,
            duration: duration,
            ease: ANIMATION_CONFIG.easing.bounce
        });
    },
    
    // Stagger animation for multiple elements
    staggerIn(elements, duration = ANIMATION_CONFIG.duration.base, stagger = ANIMATION_CONFIG.stagger.base) {
        gsap.to(elements, {
            y: 0,
            opacity: 1,
            duration: duration,
            ease: ANIMATION_CONFIG.easing.base,
            stagger: stagger
        });
    }
};

// =============================================================================
// PERFORMANCE OPTIMIZATION
// =============================================================================
// Refresh ScrollTrigger on window resize
window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
});

// Kill animations on page unload for better performance
window.addEventListener('beforeunload', () => {
    gsap.killTweensOf("*");
    ScrollTrigger.killAll();
});
