// =============================================================================
// MIXINS - RIO Middle East Trading LLC
// =============================================================================

// Media Queries
// -----------------------------------------------------------------------------
@mixin media-breakpoint-up($name) {
  $min: map-get($grid-breakpoints, $name);
  @if $min != 0 {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-down($name) {
  $max: map-get($grid-breakpoints, $name) - 0.02;
  @media (max-width: $max) {
    @content;
  }
}

@mixin media-breakpoint-between($lower, $upper) {
  $min: map-get($grid-breakpoints, $lower);
  $max: map-get($grid-breakpoints, $upper) - 0.02;
  
  @if $min != 0 {
    @media (min-width: $min) and (max-width: $max) {
      @content;
    }
  } @else {
    @media (max-width: $max) {
      @content;
    }
  }
}

// Typography
// -----------------------------------------------------------------------------
@mixin font-size($size, $line-height: null) {
  font-size: $size;
  @if $line-height {
    line-height: $line-height;
  }
}

@mixin heading-style($size: $h3-font-size, $weight: $font-weight-semibold, $line-height: 1.2) {
  font-family: $font-family-headings;
  font-size: $size;
  font-weight: $weight;
  line-height: $line-height;
  color: $headings-color;
  margin-bottom: 1rem;
}

@mixin text-truncate($lines: 1) {
  @if $lines == 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

// Layout & Positioning
// -----------------------------------------------------------------------------
@mixin center-absolute {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin center-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// Buttons & Interactive Elements
// -----------------------------------------------------------------------------
@mixin button-variant($bg, $border: $bg, $color: #fff, $hover-bg: darken($bg, 7.5%), $hover-border: darken($border, 10%), $hover-color: $color) {
  color: $color;
  background-color: $bg;
  border-color: $border;
  
  &:hover,
  &:focus {
    color: $hover-color;
    background-color: $hover-bg;
    border-color: $hover-border;
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($bg, 0.25);
  }
  
  &:active {
    background-color: darken($bg, 10%);
    border-color: darken($border, 12.5%);
  }
  
  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

@mixin button-outline-variant($color, $hover-bg: $color, $hover-color: #fff) {
  color: $color;
  background-color: transparent;
  border-color: $color;
  
  &:hover,
  &:focus {
    color: $hover-color;
    background-color: $hover-bg;
    border-color: $hover-bg;
  }
  
  &:focus {
    box-shadow: 0 0 0 0.2rem rgba($color, 0.25);
  }
  
  &:active {
    background-color: darken($hover-bg, 10%);
    border-color: darken($hover-bg, 10%);
  }
}

// Cards & Containers
// -----------------------------------------------------------------------------
@mixin card-hover-effect {
  transition: $transition-base;
  
  &:hover {
    transform: translateY(-0.25rem);
    box-shadow: $box-shadow-lg;
  }
}

@mixin glass-effect($opacity: 0.1) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// Animations
// -----------------------------------------------------------------------------
@mixin fade-in($duration: $animation-duration-base, $delay: 0s) {
  opacity: 0;
  animation: fadeIn $duration $animation-easing-base $delay forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@mixin slide-up($duration: $animation-duration-base, $delay: 0s, $distance: 2rem) {
  opacity: 0;
  transform: translateY($distance);
  animation: slideUp $duration $animation-easing-base $delay forwards;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Utilities
// -----------------------------------------------------------------------------
@mixin visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// RTL Support
// -----------------------------------------------------------------------------
@mixin rtl {
  [dir='rtl'] & {
    @content;
  }
}

@mixin ltr {
  [dir='ltr'] & {
    @content;
  }
}

// Focus States
// -----------------------------------------------------------------------------
@mixin focus-ring($color: $accent, $width: 0.2rem) {
  &:focus {
    outline: none;
    box-shadow: 0 0 0 $width rgba($color, 0.25);
  }
}

// Gradient Backgrounds
// -----------------------------------------------------------------------------
@mixin gradient-bg($start-color, $end-color, $direction: 135deg) {
  background: linear-gradient($direction, $start-color 0%, $end-color 100%);
}

// Container Queries (Future-proofing)
// -----------------------------------------------------------------------------
@mixin container-query($min-width) {
  @container (min-width: #{$min-width}) {
    @content;
  }
}
