// =============================================================================
// LAYOUT - Global Layout Styles
// =============================================================================

// Base Layout
// -----------------------------------------------------------------------------
html {
  scroll-behavior: smooth;
  font-size: 16px;
  
  @include media-breakpoint-down(sm) {
    font-size: 14px;
  }
}

body {
  font-family: $font-family-base;
  font-size: $font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-base;
  color: $dark;
  background-color: $light;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Skip to content link for accessibility
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: $primary;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: $border-radius;
  z-index: 1000;
  
  &:focus {
    top: 6px;
  }
}

// Main content wrapper
.main-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

// Container modifications
.container-fluid {
  padding-left: 1rem;
  padding-right: 1rem;
  
  @include media-breakpoint-up(sm) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  @include media-breakpoint-up(lg) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

// Section spacing
.section {
  padding: 4rem 0;
  
  @include media-breakpoint-up(md) {
    padding: 6rem 0;
  }
  
  @include media-breakpoint-up(lg) {
    padding: 8rem 0;
  }
  
  &--sm {
    padding: 2rem 0;
    
    @include media-breakpoint-up(md) {
      padding: 3rem 0;
    }
  }
  
  &--lg {
    padding: 6rem 0;
    
    @include media-breakpoint-up(md) {
      padding: 8rem 0;
    }
    
    @include media-breakpoint-up(lg) {
      padding: 10rem 0;
    }
  }
}

// Grid system enhancements
.row {
  --bs-gutter-x: #{$grid-gutter-width};
  
  @include media-breakpoint-down(md) {
    --bs-gutter-x: 1rem;
  }
}

// Utility classes for spacing
.spacer {
  &--xs { height: 1rem; }
  &--sm { height: 2rem; }
  &--md { height: 3rem; }
  &--lg { height: 4rem; }
  &--xl { height: 6rem; }
  &--xxl { height: 8rem; }
}

// Background utilities
.bg-gradient-primary {
  @include gradient-bg($primary, lighten($primary, 10%));
}

.bg-gradient-accent {
  @include gradient-bg($accent, lighten($accent, 10%));
}

.bg-pattern {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    pointer-events: none;
  }
  
  > * {
    position: relative;
    z-index: 1;
  }
}

// Sticky elements
.sticky-top-custom {
  position: sticky;
  top: 100px; // Account for header height
  z-index: $zindex-sticky;
}

// Overflow utilities
.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

// RTL Layout Support
[dir='rtl'] {
  .container,
  .container-fluid {
    text-align: right;
  }
  
  .row {
    direction: rtl;
  }
  
  .col,
  [class*='col-'] {
    direction: ltr;
    text-align: right;
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .section {
    padding: 1rem 0;
  }
  
  .btn {
    display: none;
  }
}
