// =============================================================================
// RTL SUPPORT - Right-to-Left Language Support
// =============================================================================

[dir='rtl'] {
  // Base text direction
  body {
    text-align: right;
    direction: rtl;
  }
  
  // Typography adjustments
  h1, h2, h3, h4, h5, h6 {
    text-align: right;
  }
  
  // Navigation
  .navbar {
    .navbar-nav {
      .nav-link {
        text-align: right;
      }
    }
    
    .navbar-brand {
      margin-right: 0;
      margin-left: auto;
    }
    
    .navbar-toggler {
      margin-left: 0;
      margin-right: auto;
    }
  }
  
  // Dropdown menus
  .dropdown-menu {
    text-align: right;
    
    .dropdown-item {
      text-align: right;
    }
  }
  
  // Breadcrumbs
  .breadcrumb {
    .breadcrumb-item {
      &::before {
        content: '\\';
        transform: scaleX(-1);
      }
    }
  }
  
  // Cards
  .card {
    text-align: right;
    
    &--division {
      border-left: none;
      border-right: 4px solid $accent;
    }
    
    .card-header,
    .card-body,
    .card-footer {
      text-align: right;
    }
  }
  
  // Buttons with icons
  .btn {
    svg {
      margin-left: 0.5rem;
      margin-right: 0;
    }
    
    &:first-child svg {
      margin-left: 0;
      margin-right: 0.5rem;
    }
  }
  
  // Forms
  .form-label {
    text-align: right;
  }
  
  .form-control {
    text-align: right;
  }
  
  .form-check {
    text-align: right;
    
    .form-check-input {
      margin-left: 0.25rem;
      margin-right: 0;
      float: right;
    }
    
    .form-check-label {
      padding-left: 0;
      padding-right: 1.25rem;
    }
  }
  
  // Modal
  .modal {
    .modal-header {
      text-align: right;
      
      .btn-close {
        margin-left: 0;
        margin-right: auto;
      }
    }
    
    .modal-body {
      text-align: right;
    }
    
    .modal-footer {
      justify-content: flex-start;
      
      .btn {
        margin-left: 0.5rem;
        margin-right: 0;
        
        &:first-child {
          margin-left: 0;
        }
      }
    }
  }
  
  // Lists
  ul, ol {
    padding-left: 0;
    padding-right: 2rem;
  }
  
  // Tables
  .table {
    text-align: right;
    
    th, td {
      text-align: right;
    }
  }
  
  // Hero section
  .hero {
    &__content {
      text-align: right;
    }
    
    &__actions {
      justify-content: flex-end;
      
      @include media-breakpoint-down(sm) {
        align-items: flex-end;
      }
    }
  }
  
  // Trust strip
  .trust-strip {
    &__logos {
      direction: rtl;
    }
  }
  
  // Stats
  .stats {
    &__item {
      text-align: right;
    }
  }
  
  // Why RIO section
  .why-rio {
    &__item {
      text-align: right;
    }
  }
  
  // Contact info
  .contact-info {
    &__item {
      .content {
        text-align: right;
      }
    }
  }
  
  // Footer
  .footer {
    text-align: right;
    
    .footer-links {
      text-align: right;
    }
    
    .footer-bottom {
      text-align: center; // Keep center for copyright
    }
  }
  
  // Language switcher
  .language-switcher {
    .dropdown-toggle {
      &::after {
        margin-left: 0;
        margin-right: 0.5rem;
      }
    }
  }
  
  // Divisions navigation
  .divisions-nav {
    .nav-tabs {
      .nav-link {
        text-align: right;
      }
    }
  }
  
  // Partners filter
  .partners-filter {
    .filter-buttons {
      justify-content: center; // Keep center for filters
    }
  }
  
  // Animations adjustments for RTL
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideInLeft {
    from {
      transform: translateX(-100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  // Flip animations for RTL
  .slide-in-left {
    animation: slideInRight 0.6s ease-out;
  }
  
  .slide-in-right {
    animation: slideInLeft 0.6s ease-out;
  }
  
  // Utility classes for RTL
  .text-start {
    text-align: right !important;
  }
  
  .text-end {
    text-align: left !important;
  }
  
  .float-start {
    float: right !important;
  }
  
  .float-end {
    float: left !important;
  }
  
  // Margin and padding logical properties
  .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
  }
  
  .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
  
  .ps-0 { padding-right: 0 !important; }
  .ps-1 { padding-right: 0.25rem !important; }
  .ps-2 { padding-right: 0.5rem !important; }
  .ps-3 { padding-right: 1rem !important; }
  .ps-4 { padding-right: 1.5rem !important; }
  .ps-5 { padding-right: 3rem !important; }
  
  .pe-0 { padding-left: 0 !important; }
  .pe-1 { padding-left: 0.25rem !important; }
  .pe-2 { padding-left: 0.5rem !important; }
  .pe-3 { padding-left: 1rem !important; }
  .pe-4 { padding-left: 1.5rem !important; }
  .pe-5 { padding-left: 3rem !important; }
}
