// =============================================================================
// VARIABLES - RIO Middle East Trading LLC
// =============================================================================

// Color Palette
// -----------------------------------------------------------------------------
$primary: #0b4d8c !default; // Deep tech blue
$accent: #00a3c4 !default; // Teal/industrial
$dark: #0e1b2a !default; // Dark navy
$light: #f5f7fa !default; // Light gray background
$neutral-100: #f5f7fa !default;
$neutral-200: #e6ecf1 !default;
$neutral-500: #94a3b8 !default;

// Feedback Colors
$success: #19a974 !default;
$warning: #f59e0b !default;
$danger: #ef4444 !default;
$info: $accent !default;

// Bootstrap Color Overrides
$blue: $primary;
$teal: $accent;
$gray-100: $neutral-100;
$gray-200: $neutral-200;
$gray-500: $neutral-500;
$gray-900: $dark;

// Typography
// -----------------------------------------------------------------------------
$font-family-headings: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
  'Helvetica Neue', Arial, sans-serif !default;
$font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue',
  Arial, sans-serif !default;

$font-size-base: 1rem !default; // 16px
$font-size-sm: 0.875rem !default; // 14px
$font-size-lg: 1.125rem !default; // 18px

$font-weight-light: 300 !default;
$font-weight-normal: 400 !default;
$font-weight-medium: 500 !default;
$font-weight-semibold: 600 !default;
$font-weight-bold: 700 !default;

$line-height-base: 1.6 !default;
$line-height-sm: 1.4 !default;
$line-height-lg: 1.8 !default;

// Headings
$h1-font-size: 3rem !default; // 48px
$h2-font-size: 2.5rem !default; // 40px
$h3-font-size: 2rem !default; // 32px
$h4-font-size: 1.5rem !default; // 24px
$h5-font-size: 1.25rem !default; // 20px
$h6-font-size: 1rem !default; // 16px

$headings-font-family: $font-family-headings;
$headings-font-weight: $font-weight-semibold;
$headings-line-height: 1.2;
$headings-color: $dark;

// Spacing
// -----------------------------------------------------------------------------
$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
  6: $spacer * 4,
  7: $spacer * 5,
  8: $spacer * 6,
) !default;

// Grid & Layout
// -----------------------------------------------------------------------------
$grid-gutter-width: 2rem !default;
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px,
) !default;

// Border Radius
// -----------------------------------------------------------------------------
$border-radius: 0.5rem !default;
$border-radius-sm: 0.25rem !default;
$border-radius-lg: 1rem !default;
$border-radius-xl: 1.5rem !default;

// Shadows
// -----------------------------------------------------------------------------
$box-shadow-sm: 0 0.125rem 0.25rem rgba($dark, 0.075) !default;
$box-shadow: 0 0.5rem 1rem rgba($dark, 0.15) !default;
$box-shadow-lg: 0 1rem 3rem rgba($dark, 0.175) !default;

// Transitions
// -----------------------------------------------------------------------------
$transition-base: all 0.3s ease-in-out !default;
$transition-fast: all 0.15s ease-in-out !default;
$transition-slow: all 0.6s ease-in-out !default;

// Component Specific
// -----------------------------------------------------------------------------
// Buttons
$btn-padding-y: 0.75rem !default;
$btn-padding-x: 1.5rem !default;
$btn-font-weight: $font-weight-medium !default;
$btn-border-radius: $border-radius !default;

// Cards
$card-border-radius: $border-radius-lg !default;
$card-box-shadow: $box-shadow !default;
$card-bg: #fff !default;

// Navigation
$navbar-padding-y: 1rem !default;
$navbar-brand-font-size: 1.5rem !default;
$navbar-brand-font-weight: $font-weight-bold !default;

// Forms
$input-padding-y: 0.75rem !default;
$input-padding-x: 1rem !default;
$input-border-radius: $border-radius !default;
$input-focus-border-color: $accent !default;

// Z-index layers
// -----------------------------------------------------------------------------
$zindex-dropdown: 1000 !default;
$zindex-sticky: 1020 !default;
$zindex-fixed: 1030 !default;
$zindex-modal-backdrop: 1040 !default;
$zindex-modal: 1050 !default;
$zindex-popover: 1060 !default;
$zindex-tooltip: 1070 !default;

// Breakpoints (Bootstrap defaults)
// -----------------------------------------------------------------------------
$grid-breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px,
) !default;

// Animation & Motion
// -----------------------------------------------------------------------------
$animation-duration-fast: 0.15s !default;
$animation-duration-base: 0.3s !default;
$animation-duration-slow: 0.6s !default;
$animation-duration-slower: 1s !default;

$animation-easing-base: cubic-bezier(0.25, 0.46, 0.45, 0.94) !default; // easeOutQuad
$animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55) !default;
$animation-easing-sharp: cubic-bezier(0.4, 0, 0.2, 1) !default;
