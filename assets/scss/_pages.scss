// =============================================================================
// PAGES - Page-specific Styles
// =============================================================================

// Homepage
// -----------------------------------------------------------------------------
.hero {
  position: relative;
  min-height: 100vh;
  @include center-flex();
  background: linear-gradient(135deg, rgba($primary, 0.9) 0%, rgba($accent, 0.8) 100%);
  color: white;
  text-align: center;
  overflow: hidden;
  
  &__background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    z-index: -2;
  }
  
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba($primary, 0.9) 0%, rgba($accent, 0.8) 100%);
    z-index: -1;
  }
  
  &__content {
    max-width: 800px;
    padding: 2rem;
    z-index: 1;
  }
  
  &__title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: $font-weight-bold;
    margin-bottom: 1.5rem;
    line-height: 1.1;
  }
  
  &__subtitle {
    font-size: clamp(1.125rem, 2vw, 1.5rem);
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.4;
  }
  
  &__actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    
    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: center;
    }
  }
  
  // Scroll indicator
  &__scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    opacity: 0.7;
    animation: bounce 2s infinite;
    
    svg {
      width: 1.5rem;
      height: 1.5rem;
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

// Trust strip
.trust-strip {
  background: white;
  padding: 2rem 0;
  border-bottom: 1px solid $neutral-200;
  
  &__logos {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    
    @include media-breakpoint-down(md) {
      gap: 2rem;
    }
  }
  
  &__logo {
    height: 60px;
    filter: grayscale(100%);
    opacity: 0.6;
    transition: $transition-base;
    
    &:hover {
      filter: grayscale(0%);
      opacity: 1;
    }
  }
}

// Stats section
.stats {
  background: $primary;
  color: white;
  padding: 4rem 0;
  
  &__item {
    text-align: center;
    
    .stats__number {
      font-size: 3rem;
      font-weight: $font-weight-bold;
      display: block;
      margin-bottom: 0.5rem;
      color: $accent;
    }
    
    .stats__label {
      font-size: $font-size-lg;
      opacity: 0.9;
    }
  }
}

// Why RIO section
.why-rio {
  &__item {
    text-align: center;
    padding: 2rem 1rem;
    
    .icon {
      width: 4rem;
      height: 4rem;
      background: rgba($accent, 0.1);
      border-radius: 50%;
      @include center-flex();
      margin: 0 auto 1.5rem;
      
      svg {
        width: 2rem;
        height: 2rem;
        color: $accent;
      }
    }
    
    h3 {
      @include heading-style($h4-font-size);
      margin-bottom: 1rem;
    }
    
    p {
      color: $neutral-500;
      line-height: 1.6;
    }
  }
}

// Divisions Page
// -----------------------------------------------------------------------------
.divisions-hero {
  background: linear-gradient(135deg, $primary 0%, $accent 100%);
  color: white;
  padding: 6rem 0 4rem;
  text-align: center;
  
  h1 {
    font-size: clamp(2rem, 4vw, 3rem);
    margin-bottom: 1rem;
  }
  
  p {
    font-size: $font-size-lg;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
  }
}

.divisions-nav {
  background: white;
  border-bottom: 1px solid $neutral-200;
  position: sticky;
  top: 80px; // Account for main nav
  z-index: $zindex-sticky;
  
  .nav-tabs {
    border-bottom: none;
    justify-content: center;
    
    .nav-link {
      border: none;
      color: $dark;
      font-weight: $font-weight-medium;
      padding: 1rem 1.5rem;
      
      &.active {
        color: $primary;
        border-bottom: 2px solid $primary;
        background: transparent;
      }
      
      &:hover {
        color: $primary;
        border-color: transparent;
      }
    }
  }
}

.division-content {
  padding: 4rem 0;
  
  &__header {
    text-align: center;
    margin-bottom: 3rem;
    
    h2 {
      @include heading-style($h2-font-size);
      margin-bottom: 1rem;
    }
    
    p {
      font-size: $font-size-lg;
      color: $neutral-500;
      max-width: 800px;
      margin: 0 auto;
    }
  }
  
  &__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }
}

// Partners Page
// -----------------------------------------------------------------------------
.partners-filter {
  background: white;
  padding: 2rem 0;
  border-bottom: 1px solid $neutral-200;
  
  .filter-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    
    .btn {
      border-radius: 2rem;
      padding: 0.5rem 1.5rem;
      
      &.active {
        background: $primary;
        color: white;
      }
    }
  }
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  padding: 3rem 0;
}

// Contact Page
// -----------------------------------------------------------------------------
.contact-hero {
  background: $neutral-100;
  padding: 4rem 0;
  text-align: center;
  
  h1 {
    @include heading-style($h1-font-size);
    margin-bottom: 1rem;
  }
  
  p {
    font-size: $font-size-lg;
    color: $neutral-500;
    max-width: 600px;
    margin: 0 auto;
  }
}

.contact-info {
  background: white;
  border-radius: $border-radius-lg;
  padding: 2rem;
  box-shadow: $box-shadow;
  
  &__item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .icon {
      width: 3rem;
      height: 3rem;
      background: rgba($primary, 0.1);
      border-radius: 50%;
      @include center-flex();
      flex-shrink: 0;
      
      svg {
        width: 1.5rem;
        height: 1.5rem;
        color: $primary;
      }
    }
    
    .content {
      h4 {
        @include heading-style($h5-font-size);
        margin-bottom: 0.5rem;
      }
      
      p {
        color: $neutral-500;
        margin: 0;
        line-height: 1.5;
      }
      
      a {
        color: $primary;
        text-decoration: none;
        
        &:hover {
          color: $accent;
        }
      }
    }
  }
}

.contact-form {
  background: white;
  border-radius: $border-radius-lg;
  padding: 2rem;
  box-shadow: $box-shadow;
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .form-control {
    &.is-invalid {
      border-color: $danger;
    }
    
    &.is-valid {
      border-color: $success;
    }
  }
  
  .invalid-feedback {
    color: $danger;
    font-size: $font-size-sm;
    margin-top: 0.25rem;
  }
}

.map-container {
  height: 400px;
  border-radius: $border-radius-lg;
  overflow: hidden;
  box-shadow: $box-shadow;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
