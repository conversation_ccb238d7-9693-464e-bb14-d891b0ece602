// =============================================================================
// MAIN STYLESHEET - RIO Middle East Trading LLC
// =============================================================================

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@600;700&display=swap');

// Import our custom variables and mixins first
@import 'variables';
@import 'mixins';

// Import Bootstrap with our custom variables
@import '~bootstrap/scss/functions';
@import '~bootstrap/scss/variables';
@import '~bootstrap/scss/mixins';
@import '~bootstrap/scss/root';
@import '~bootstrap/scss/utilities';
@import '~bootstrap/scss/reboot';
@import '~bootstrap/scss/type';
@import '~bootstrap/scss/images';
@import '~bootstrap/scss/containers';
@import '~bootstrap/scss/grid';
@import '~bootstrap/scss/tables';
@import '~bootstrap/scss/forms';
@import '~bootstrap/scss/buttons';
@import '~bootstrap/scss/transitions';
@import '~bootstrap/scss/dropdown';
@import '~bootstrap/scss/button-group';
@import '~bootstrap/scss/nav';
@import '~bootstrap/scss/navbar';
@import '~bootstrap/scss/card';
@import '~bootstrap/scss/accordion';
@import '~bootstrap/scss/breadcrumb';
@import '~bootstrap/scss/pagination';
@import '~bootstrap/scss/badge';
@import '~bootstrap/scss/alert';
@import '~bootstrap/scss/progress';
@import '~bootstrap/scss/list-group';
@import '~bootstrap/scss/close';
@import '~bootstrap/scss/toasts';
@import '~bootstrap/scss/modal';
@import '~bootstrap/scss/tooltip';
@import '~bootstrap/scss/popover';
@import '~bootstrap/scss/carousel';
@import '~bootstrap/scss/spinners';
@import '~bootstrap/scss/offcanvas';
@import '~bootstrap/scss/placeholders';
@import '~bootstrap/scss/helpers';
@import '~bootstrap/scss/utilities/api';

// Import our custom styles
@import 'layout';
@import 'components';
@import 'pages';

// RTL Support
// -----------------------------------------------------------------------------
@import 'rtl';

// Utility Classes
// -----------------------------------------------------------------------------
.text-primary { color: $primary !important; }
.text-accent { color: $accent !important; }
.bg-primary { background-color: $primary !important; }
.bg-accent { background-color: $accent !important; }
.bg-light-gray { background-color: $neutral-100 !important; }

// Animation utilities
.fade-in { @include fade-in(); }
.slide-up { @include slide-up(); }

// Spacing utilities
.py-6 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
.py-7 { padding-top: 5rem !important; padding-bottom: 5rem !important; }
.py-8 { padding-top: 6rem !important; padding-bottom: 6rem !important; }
.my-6 { margin-top: 4rem !important; margin-bottom: 4rem !important; }
.my-7 { margin-top: 5rem !important; margin-bottom: 5rem !important; }
.my-8 { margin-top: 6rem !important; margin-bottom: 6rem !important; }

// Text utilities
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-gradient {
  background: linear-gradient(135deg, $primary 0%, $accent 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// Border utilities
.border-primary { border-color: $primary !important; }
.border-accent { border-color: $accent !important; }

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: $neutral-100;
}

::-webkit-scrollbar-thumb {
  background: $neutral-500;
  border-radius: 4px;
  
  &:hover {
    background: $primary;
  }
}

// Selection styles
::selection {
  background: rgba($primary, 0.2);
  color: $dark;
}

::-moz-selection {
  background: rgba($primary, 0.2);
  color: $dark;
}

// Focus styles for better accessibility
*:focus {
  outline: 2px solid $accent;
  outline-offset: 2px;
}

// Remove focus outline for mouse users
.js-focus-visible *:focus:not(.focus-visible) {
  outline: none;
}

// Print styles
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .no-print {
    display: none !important;
  }
  
  @page {
    margin: 0.5in;
  }
  
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  
  h2,
  h3 {
    page-break-after: avoid;
  }
}
