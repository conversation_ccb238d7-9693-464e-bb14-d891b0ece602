// =============================================================================
// COMPONENTS - Reusable Component Styles
// =============================================================================

// Buttons
// -----------------------------------------------------------------------------
.btn {
  font-weight: $btn-font-weight;
  border-radius: $btn-border-radius;
  padding: $btn-padding-y $btn-padding-x;
  transition: $transition-base;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  
  @include focus-ring();
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  // Size variants
  &--sm {
    padding: 0.5rem 1rem;
    font-size: $font-size-sm;
  }
  
  &--lg {
    padding: 1rem 2rem;
    font-size: $font-size-lg;
  }
  
  // Style variants
  &--ghost {
    background: transparent;
    border: none;
    color: $primary;
    
    &:hover {
      background: rgba($primary, 0.1);
    }
  }
}

.btn-primary {
  @include button-variant($primary);
}

.btn-outline-primary {
  @include button-outline-variant($primary);
}

.btn-accent {
  @include button-variant($accent);
}

// Cards
// -----------------------------------------------------------------------------
.card {
  border: none;
  border-radius: $card-border-radius;
  box-shadow: $card-box-shadow;
  background: $card-bg;
  transition: $transition-base;
  
  &--hover {
    @include card-hover-effect();
  }
  
  &--feature {
    text-align: center;
    padding: 2rem;
    
    .card-icon {
      width: 4rem;
      height: 4rem;
      margin: 0 auto 1.5rem;
      background: rgba($primary, 0.1);
      border-radius: 50%;
      @include center-flex();
      
      svg {
        width: 2rem;
        height: 2rem;
        color: $primary;
      }
    }
    
    .card-title {
      @include heading-style($h5-font-size);
      margin-bottom: 1rem;
    }
    
    .card-text {
      color: $neutral-500;
      margin-bottom: 1.5rem;
    }
  }
  
  &--division {
    border-left: 4px solid $accent;
    
    .card-header {
      background: rgba($accent, 0.05);
      border-bottom: 1px solid rgba($accent, 0.1);
    }
  }
  
  &--partner {
    @include center-flex();
    padding: 2rem;
    min-height: 150px;
    cursor: pointer;
    
    img {
      max-width: 100%;
      max-height: 80px;
      filter: grayscale(100%);
      transition: $transition-base;
    }
    
    &:hover img {
      filter: grayscale(0%);
    }
  }
}

// Navigation
// -----------------------------------------------------------------------------
.navbar {
  padding: $navbar-padding-y 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba($neutral-200, 0.5);
  
  &.scrolled {
    box-shadow: $box-shadow;
  }
  
  .navbar-brand {
    font-size: $navbar-brand-font-size;
    font-weight: $navbar-brand-font-weight;
    color: $primary;
    text-decoration: none;
    
    &:hover {
      color: $accent;
    }
  }
  
  .navbar-nav {
    .nav-link {
      font-weight: $font-weight-medium;
      color: $dark;
      padding: 0.5rem 1rem;
      transition: $transition-base;
      
      &:hover,
      &.active {
        color: $primary;
      }
    }
  }
  
  .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    
    &:focus {
      box-shadow: none;
    }
  }
}

// Top bar
.top-bar {
  background: $dark;
  color: white;
  padding: 0.5rem 0;
  font-size: $font-size-sm;
  
  a {
    color: white;
    text-decoration: none;
    
    &:hover {
      color: $accent;
    }
  }
}

// Footer
// -----------------------------------------------------------------------------
.footer {
  background: $dark;
  color: white;
  padding: 3rem 0 1rem;
  
  .footer-brand {
    font-size: $h4-font-size;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: 1rem;
  }
  
  .footer-links {
    list-style: none;
    padding: 0;
    
    li {
      margin-bottom: 0.5rem;
    }
    
    a {
      color: rgba(white, 0.8);
      text-decoration: none;
      transition: $transition-base;
      
      &:hover {
        color: $accent;
      }
    }
  }
  
  .footer-bottom {
    border-top: 1px solid rgba(white, 0.1);
    padding-top: 1rem;
    margin-top: 2rem;
    text-align: center;
    color: rgba(white, 0.6);
    font-size: $font-size-sm;
  }
}

// Breadcrumbs
// -----------------------------------------------------------------------------
.breadcrumb {
  background: transparent;
  padding: 1rem 0;
  margin-bottom: 0;
  
  .breadcrumb-item {
    &.active {
      color: $neutral-500;
    }
    
    a {
      color: $primary;
      text-decoration: none;
      
      &:hover {
        color: $accent;
      }
    }
  }
}

// Modals
// -----------------------------------------------------------------------------
.modal {
  .modal-content {
    border: none;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-lg;
  }
  
  .modal-header {
    border-bottom: 1px solid $neutral-200;
    
    .modal-title {
      @include heading-style($h4-font-size);
      margin-bottom: 0;
    }
    
    .btn-close {
      @include focus-ring();
    }
  }
  
  .modal-footer {
    border-top: 1px solid $neutral-200;
  }
}

// Forms
// -----------------------------------------------------------------------------
.form-control {
  border-radius: $input-border-radius;
  padding: $input-padding-y $input-padding-x;
  border: 1px solid $neutral-200;
  transition: $transition-base;
  
  &:focus {
    border-color: $input-focus-border-color;
    box-shadow: 0 0 0 0.2rem rgba($input-focus-border-color, 0.25);
  }
}

.form-label {
  font-weight: $font-weight-medium;
  color: $dark;
  margin-bottom: 0.5rem;
}

.form-floating {
  .form-control {
    &:focus ~ label,
    &:not(:placeholder-shown) ~ label {
      color: $accent;
    }
  }
}

// Alerts & Toasts
// -----------------------------------------------------------------------------
.alert {
  border: none;
  border-radius: $border-radius;
  
  &-success {
    background: rgba($success, 0.1);
    color: darken($success, 20%);
  }
  
  &-warning {
    background: rgba($warning, 0.1);
    color: darken($warning, 20%);
  }
  
  &-danger {
    background: rgba($danger, 0.1);
    color: darken($danger, 20%);
  }
}

.toast {
  border: none;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
}

// Loading states
// -----------------------------------------------------------------------------
.loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid $neutral-200;
    border-top-color: $primary;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Language switcher
// -----------------------------------------------------------------------------
.language-switcher {
  .dropdown-toggle {
    background: transparent;
    border: 1px solid $neutral-200;
    color: $dark;
    padding: 0.25rem 0.75rem;
    font-size: $font-size-sm;

    &::after {
      margin-left: 0.5rem;
    }
  }

  .dropdown-menu {
    border: none;
    box-shadow: $box-shadow;
    border-radius: $border-radius;
    min-width: 100px;
  }

  .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: $font-size-sm;

    &:hover {
      background: rgba($primary, 0.1);
      color: $primary;
    }

    &.active {
      background: $primary;
      color: white;
    }
  }
}
