/* =============================================================================
   RIO Middle East Trading LLC - Main Stylesheet
   ============================================================================= */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@600;700&display=swap');

/* =============================================================================
   CSS VARIABLES
   ============================================================================= */
:root {
  /* Colors */
  --primary: #0b4d8c;
  --accent: #00a3c4;
  --dark: #0e1b2a;
  --light: #f5f7fa;
  --neutral-100: #f5f7fa;
  --neutral-200: #e6ecf1;
  --neutral-500: #94a3b8;
  --success: #19a974;
  --warning: #f59e0b;
  --danger: #ef4444;
  
  /* Typography */
  --font-family-headings: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-base: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Spacing */
  --spacer: 1rem;
  
  /* Transitions */
  --transition-base: all 0.3s ease-in-out;
  --transition-fast: all 0.15s ease-in-out;
  
  /* Shadows */
  --box-shadow: 0 0.5rem 1rem rgba(14, 27, 42, 0.15);
  --box-shadow-lg: 0 1rem 3rem rgba(14, 27, 42, 0.175);
  
  /* Border Radius */
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
}

/* =============================================================================
   BASE STYLES
   ============================================================================= */
html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-base);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--dark);
  background-color: var(--light);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius);
  z-index: 1000;
}

.skip-to-content:focus {
  top: 6px;
}

/* =============================================================================
   TYPOGRAPHY
   ============================================================================= */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-headings);
  font-weight: 600;
  line-height: 1.2;
  color: var(--dark);
  margin-bottom: 1rem;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

@media (max-width: 768px) {
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}

/* =============================================================================
   LAYOUT
   ============================================================================= */
.main-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

.section {
  padding: 4rem 0;
}

@media (min-width: 768px) {
  .section {
    padding: 6rem 0;
  }
}

@media (min-width: 992px) {
  .section {
    padding: 8rem 0;
  }
}

.section--sm {
  padding: 2rem 0;
}

@media (min-width: 768px) {
  .section--sm {
    padding: 3rem 0;
  }
}

/* =============================================================================
   NAVIGATION
   ============================================================================= */
.top-bar {
  background: var(--dark);
  color: white;
  padding: 0.5rem 0;
  font-size: 0.875rem;
}

.top-bar a {
  color: white;
  text-decoration: none;
  transition: var(--transition-base);
}

.top-bar a:hover {
  color: var(--accent);
}

.navbar {
  padding: 1rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(230, 236, 241, 0.5);
  transition: var(--transition-base);
}

.navbar.scrolled {
  box-shadow: var(--box-shadow);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary);
  text-decoration: none;
}

.navbar-brand:hover {
  color: var(--accent);
}

.navbar-nav .nav-link {
  font-weight: 500;
  color: var(--dark);
  padding: 0.5rem 1rem;
  transition: var(--transition-base);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--primary);
}

/* =============================================================================
   BUTTONS
   ============================================================================= */
.btn {
  font-weight: 500;
  border-radius: var(--border-radius);
  padding: 0.75rem 1.5rem;
  transition: var(--transition-base);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border: 1px solid transparent;
  cursor: pointer;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  color: white;
  background-color: #094080;
  border-color: #083a7a;
}

.btn-accent {
  color: white;
  background-color: var(--accent);
  border-color: var(--accent);
}

.btn-accent:hover {
  color: white;
  background-color: #0092b0;
  border-color: #008aa5;
}

.btn-outline-primary {
  color: var(--primary);
  background-color: transparent;
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  color: white;
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-light {
  color: white;
  background-color: transparent;
  border-color: white;
}

.btn-outline-light:hover {
  color: var(--primary);
  background-color: white;
  border-color: white;
}

.btn-light {
  color: var(--dark);
  background-color: white;
  border-color: white;
}

.btn-light:hover {
  color: var(--dark);
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

/* =============================================================================
   CARDS
   ============================================================================= */
.card {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  background: white;
  transition: var(--transition-base);
}

.card--hover:hover {
  transform: translateY(-0.25rem);
  box-shadow: var(--box-shadow-lg);
}

.card--feature {
  text-align: center;
  padding: 2rem;
}

.card--feature .card-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1.5rem;
  background: rgba(11, 77, 140, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card--feature .card-icon i {
  font-size: 2rem;
  color: var(--primary);
}

.card--division {
  border-left: 4px solid var(--accent);
}

.card--division .card-header {
  background: rgba(0, 163, 196, 0.05);
  border-bottom: 1px solid rgba(0, 163, 196, 0.1);
}

.card--partner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 150px;
  cursor: pointer;
}

.card--partner img {
  max-width: 100%;
  max-height: 80px;
  filter: grayscale(100%);
  transition: var(--transition-base);
}

.card--partner:hover img {
  filter: grayscale(0%);
}

/* =============================================================================
   HERO SECTION
   ============================================================================= */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(11, 77, 140, 0.9) 0%, rgba(0, 163, 196, 0.8) 100%);
  color: white;
  text-align: center;
  overflow: hidden;
}

.hero__background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  z-index: -2;
}

.hero__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(11, 77, 140, 0.9) 0%, rgba(0, 163, 196, 0.8) 100%);
  z-index: -1;
}

.hero__content {
  max-width: 800px;
  padding: 2rem;
  z-index: 1;
}

.hero__title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.hero__subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  margin-bottom: 2.5rem;
  opacity: 0.9;
  line-height: 1.4;
}

.hero__actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 576px) {
  .hero__actions {
    flex-direction: column;
    align-items: center;
  }
}

.hero__scroll {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  opacity: 0.7;
  animation: bounce 2s infinite;
}

.hero__scroll i {
  font-size: 1.5rem;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* =============================================================================
   TRUST STRIP
   ============================================================================= */
.trust-strip {
  background: white;
  padding: 2rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.trust-strip__logos {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .trust-strip__logos {
    gap: 2rem;
  }
}

.trust-strip__logo {
  height: 60px;
  filter: grayscale(100%);
  opacity: 0.6;
  transition: var(--transition-base);
}

.trust-strip__logo:hover {
  filter: grayscale(0%);
  opacity: 1;
}

/* =============================================================================
   STATS SECTION
   ============================================================================= */
.stats {
  background: var(--primary);
  color: white;
  padding: 4rem 0;
}

.stats__item {
  text-align: center;
}

.stats__number {
  font-size: 3rem;
  font-weight: 700;
  display: block;
  margin-bottom: 0.5rem;
  color: var(--accent);
}

.stats__label {
  font-size: 1.125rem;
  opacity: 0.9;
}

/* =============================================================================
   WHY RIO SECTION
   ============================================================================= */
.why-rio__item {
  text-align: center;
  padding: 2rem 1rem;
}

.why-rio__item .icon {
  width: 4rem;
  height: 4rem;
  background: rgba(0, 163, 196, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.why-rio__item .icon i {
  font-size: 2rem;
  color: var(--accent);
}

.why-rio__item h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.why-rio__item p {
  color: var(--neutral-500);
  line-height: 1.6;
}

/* =============================================================================
   DIVISIONS PAGE
   ============================================================================= */
.divisions-hero {
  background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
  color: white;
  padding: 6rem 0 4rem;
  text-align: center;
}

.divisions-hero h1 {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1rem;
}

.divisions-hero p {
  font-size: 1.125rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

.divisions-nav {
  background: white;
  border-bottom: 1px solid var(--neutral-200);
  position: sticky;
  top: 80px;
  z-index: 1020;
}

.divisions-nav .nav-tabs {
  border-bottom: none;
  justify-content: center;
}

.divisions-nav .nav-link {
  border: none;
  color: var(--dark);
  font-weight: 500;
  padding: 1rem 1.5rem;
}

.divisions-nav .nav-link.active {
  color: var(--primary);
  border-bottom: 2px solid var(--primary);
  background: transparent;
}

.divisions-nav .nav-link:hover {
  color: var(--primary);
  border-color: transparent;
}

.division-content {
  padding: 4rem 0;
}

.division-content__header {
  text-align: center;
  margin-bottom: 3rem;
}

.division-content__header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.division-content__header p {
  font-size: 1.125rem;
  color: var(--neutral-500);
  max-width: 800px;
  margin: 0 auto;
}

.division-content__grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

/* =============================================================================
   PARTNERS PAGE
   ============================================================================= */
.partners-filter {
  background: white;
  padding: 2rem 0;
  border-bottom: 1px solid var(--neutral-200);
}

.filter-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.filter-buttons .btn {
  border-radius: 2rem;
  padding: 0.5rem 1.5rem;
}

.filter-buttons .btn.active {
  background: var(--primary);
  color: white;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 2rem;
  padding: 3rem 0;
}

/* =============================================================================
   CONTACT PAGE
   ============================================================================= */
.contact-hero {
  background: var(--neutral-100);
  padding: 4rem 0;
  text-align: center;
}

.contact-hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.contact-hero p {
  font-size: 1.125rem;
  color: var(--neutral-500);
  max-width: 600px;
  margin: 0 auto;
}

.contact-info {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.contact-info__item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-info__item:last-child {
  margin-bottom: 0;
}

.contact-info__item .icon {
  width: 3rem;
  height: 3rem;
  background: rgba(11, 77, 140, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-info__item .icon i {
  font-size: 1.5rem;
  color: var(--primary);
}

.contact-info__item .content h4 {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.contact-info__item .content p {
  color: var(--neutral-500);
  margin: 0;
  line-height: 1.5;
}

.contact-info__item .content a {
  color: var(--primary);
  text-decoration: none;
}

.contact-info__item .content a:hover {
  color: var(--accent);
}

.contact-form {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.contact-form .form-group {
  margin-bottom: 1.5rem;
}

.map-container {
  height: 400px;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  position: relative;
}

.map-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* =============================================================================
   FORMS
   ============================================================================= */
.form-control {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid var(--neutral-200);
  transition: var(--transition-base);
}

.form-control:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 0.2rem rgba(0, 163, 196, 0.25);
  outline: none;
}

.form-label {
  font-weight: 500;
  color: var(--dark);
  margin-bottom: 0.5rem;
}

.form-select {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid var(--neutral-200);
  transition: var(--transition-base);
}

.form-select:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 0.2rem rgba(0, 163, 196, 0.25);
  outline: none;
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  border-color: var(--accent);
  box-shadow: 0 0 0 0.2rem rgba(0, 163, 196, 0.25);
}

.is-invalid {
  border-color: var(--danger);
}

.is-valid {
  border-color: var(--success);
}

.invalid-feedback {
  color: var(--danger);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* =============================================================================
   FOOTER
   ============================================================================= */
.footer {
  background: var(--dark);
  color: white;
  padding: 3rem 0 1rem;
}

.footer-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition-base);
}

.footer-links a:hover {
  color: var(--accent);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
  margin-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

/* =============================================================================
   MODALS
   ============================================================================= */
.modal-content {
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
}

.modal-header {
  border-bottom: 1px solid var(--neutral-200);
}

.modal-title {
  font-size: 1.5rem;
  margin-bottom: 0;
}

.modal-footer {
  border-top: 1px solid var(--neutral-200);
}

.btn-close:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 163, 196, 0.25);
}

/* =============================================================================
   BREADCRUMBS
   ============================================================================= */
.breadcrumb {
  background: transparent;
  padding: 1rem 0;
  margin-bottom: 0;
}

.breadcrumb-item.active {
  color: var(--neutral-500);
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: var(--accent);
}

/* =============================================================================
   LANGUAGE SWITCHER
   ============================================================================= */
.language-switcher .dropdown-toggle {
  background: transparent;
  border: 1px solid var(--neutral-200);
  color: var(--dark);
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

.language-switcher .dropdown-toggle::after {
  margin-left: 0.5rem;
}

.language-switcher .dropdown-menu {
  border: none;
  box-shadow: var(--box-shadow);
  border-radius: var(--border-radius);
  min-width: 100px;
}

.language-switcher .dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.language-switcher .dropdown-item:hover {
  background: rgba(11, 77, 140, 0.1);
  color: var(--primary);
}

.language-switcher .dropdown-item.active {
  background: var(--primary);
  color: white;
}

/* =============================================================================
   ALERTS & TOASTS
   ============================================================================= */
.alert {
  border: none;
  border-radius: var(--border-radius);
}

.alert-success {
  background: rgba(25, 169, 116, 0.1);
  color: #0d5f3c;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
}

.toast {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* =============================================================================
   UTILITY CLASSES
   ============================================================================= */
.text-primary { color: var(--primary) !important; }
.text-accent { color: var(--accent) !important; }
.bg-primary { background-color: var(--primary) !important; }
.bg-accent { background-color: var(--accent) !important; }
.bg-light-gray { background-color: var(--neutral-100) !important; }
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, #0b5a9e 100%) !important;
}

.fade-in {
  opacity: 0;
  animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.slide-up {
  opacity: 0;
  transform: translateY(2rem);
  animation: slideUp 0.6s ease-out forwards;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Spacing utilities */
.py-6 { padding-top: 4rem !important; padding-bottom: 4rem !important; }
.py-7 { padding-top: 5rem !important; padding-bottom: 5rem !important; }
.py-8 { padding-top: 6rem !important; padding-bottom: 6rem !important; }

/* Text utilities */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Loading states */
.loading {
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2rem;
  height: 2rem;
  margin: -1rem 0 0 -1rem;
  border: 2px solid var(--neutral-200);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* =============================================================================
   RTL SUPPORT
   ============================================================================= */
[dir='rtl'] body {
  text-align: right;
  direction: rtl;
}

[dir='rtl'] h1,
[dir='rtl'] h2,
[dir='rtl'] h3,
[dir='rtl'] h4,
[dir='rtl'] h5,
[dir='rtl'] h6 {
  text-align: right;
}

[dir='rtl'] .navbar-brand {
  margin-right: 0;
  margin-left: auto;
}

[dir='rtl'] .navbar-toggler {
  margin-left: 0;
  margin-right: auto;
}

[dir='rtl'] .dropdown-menu {
  text-align: right;
}

[dir='rtl'] .dropdown-item {
  text-align: right;
}

[dir='rtl'] .breadcrumb-item::before {
  content: '\\';
  transform: scaleX(-1);
}

[dir='rtl'] .card {
  text-align: right;
}

[dir='rtl'] .card--division {
  border-left: none;
  border-right: 4px solid var(--accent);
}

[dir='rtl'] .hero__content {
  text-align: right;
}

[dir='rtl'] .hero__actions {
  justify-content: flex-end;
}

[dir='rtl'] .trust-strip__logos {
  direction: rtl;
}

[dir='rtl'] .stats__item {
  text-align: right;
}

[dir='rtl'] .why-rio__item {
  text-align: right;
}

[dir='rtl'] .contact-info__item .content {
  text-align: right;
}

[dir='rtl'] .footer {
  text-align: right;
}

[dir='rtl'] .footer-bottom {
  text-align: center;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */
@media (max-width: 575.98px) {
  .hero__title {
    font-size: 2rem;
  }

  .hero__subtitle {
    font-size: 1rem;
  }

  .section {
    padding: 2rem 0;
  }

  .card--feature {
    padding: 1.5rem;
  }

  .stats__number {
    font-size: 2rem;
  }

  .contact-info__item {
    flex-direction: column;
    text-align: center;
  }

  .divisions-nav .nav-link {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

@media (max-width: 767.98px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .hero {
    min-height: 80vh;
  }

  .division-content__grid {
    grid-template-columns: 1fr;
  }

  .partners-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .filter-buttons {
    flex-direction: column;
    align-items: center;
  }

  .filter-buttons .btn {
    width: 200px;
  }
}

@media (max-width: 991.98px) {
  .navbar-nav {
    text-align: center;
    margin-top: 1rem;
  }

  .navbar-nav .nav-link {
    padding: 0.75rem 1rem;
  }

  .hero__actions {
    flex-direction: column;
    align-items: center;
  }

  .trust-strip__logos {
    gap: 2rem;
  }
}

/* =============================================================================
   PRINT STYLES
   ============================================================================= */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }

  .section {
    padding: 1rem 0;
  }

  .btn {
    display: none;
  }

  .hero {
    min-height: auto;
    padding: 2rem 0;
  }

  .navbar,
  .footer,
  .modal {
    display: none;
  }
}

/* =============================================================================
   ACCESSIBILITY
   ============================================================================= */
/* Focus styles */
*:focus {
  outline: 2px solid var(--accent);
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
.js-focus-visible *:focus:not(.focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --primary: #000080;
    --accent: #006666;
    --dark: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .hero__scroll {
    animation: none;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-500);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* Selection styles */
::selection {
  background: rgba(11, 77, 140, 0.2);
  color: var(--dark);
}

::-moz-selection {
  background: rgba(11, 77, 140, 0.2);
  color: var(--dark);
}
