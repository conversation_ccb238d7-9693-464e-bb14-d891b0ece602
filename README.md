# RIO Middle East Trading LLC - Website

A modern, responsive website for RIO Middle East Trading LLC, built with HTML5, CSS3, vanilla JavaScript, Bootstrap 5, and GSAP animations.

## 🚀 Features

- **Modern Design**: Clean, professional B2B industrial design with subtle geometric accents
- **Fully Responsive**: Optimized for all devices from mobile to desktop
- **Accessibility**: WCAG 2.1 AA compliant with proper focus states and screen reader support
- **Performance**: Optimized for Lighthouse scores (Performance ≥90, Accessibility ≥95)
- **Animations**: Smooth GSAP animations with ScrollTrigger for enhanced user experience
- **RTL Support**: Full Arabic language support with RTL layout
- **SEO Optimized**: Comprehensive meta tags, JSON-LD schema, and semantic HTML

## 📁 Project Structure

```
RIO/
├── assets/
│   ├── css/
│   │   └── styles.css          # Compiled CSS styles
│   ├── scss/                   # SCSS source files (for reference)
│   │   ├── _variables.scss
│   │   ├── _mixins.scss
│   │   ├── _layout.scss
│   │   ├── _components.scss
│   │   ├── _pages.scss
│   │   ├── _rtl.scss
│   │   └── styles.scss
│   ├── js/
│   │   ├── main.js             # Main JavaScript functionality
│   │   ├── gsap-animations.js  # GSAP animations
│   │   └── components/         # Modular components (future)
│   ├── img/
│   │   └── partners/           # Partner logos
│   └── fonts/                  # Custom fonts (if needed)
├── index.html                  # Homepage
├── divisions.html              # Divisions page
├── partners.html               # Partners page
├── contact.html                # Contact page
├── privacy.html                # Privacy policy
├── package.json                # Development dependencies (optional)
└── README.md                   # This file
```

## 🎨 Design System

### Color Palette
- **Primary**: #0B4D8C (Deep tech blue)
- **Accent**: #00A3C4 (Teal/industrial)
- **Dark**: #0E1B2A (Dark navy)
- **Light**: #F5F7FA (Light gray background)
- **Success**: #19A974
- **Warning**: #F59E0B
- **Danger**: #EF4444

### Typography
- **Headings**: Inter/Poppins (600/700 weight)
- **Body**: Inter (400/500 weight)
- **Base font size**: 16px
- **Line height**: ≥1.6

## 🛠️ Development

### Prerequisites
- Modern web browser
- Local web server (optional, for development)

### Quick Start
1. Clone or download the project files
2. Open `index.html` in a web browser, or
3. Serve the files using a local web server:
   ```bash
   # Using Python 3
   python -m http.server 3000
   
   # Using Node.js (if you have live-server installed)
   npx live-server --port=3000
   
   # Using PHP
   php -S localhost:3000
   ```

### Optional: SCSS Development
If you want to modify the SCSS files and compile them:

1. Install dependencies:
   ```bash
   npm install
   ```

2. Watch for SCSS changes:
   ```bash
   npm run watch-css
   ```

3. Build for production:
   ```bash
   npm run build
   ```

## 🌐 Language Support

### Switching Languages
The website supports English and Arabic with full RTL layout support.

- Language preference is saved in localStorage
- RTL styles are automatically applied for Arabic
- Toggle between languages using the language switcher in the top bar

### Adding New Languages
1. Add language option to the dropdown in HTML files
2. Update the `LanguageSwitcher.switchLanguage()` function in `main.js`
3. Add RTL support in CSS if needed
4. Translate content (currently static in HTML)

## 📝 Content Management

### Updating Divisions
To modify division content:
1. Edit the division sections in `divisions.html`
2. Update the navigation dropdowns in all HTML files
3. Modify the division cards on the homepage in `index.html`

### Managing Partners
To add/remove partners:
1. Update the partner grid in `partners.html`
2. Add partner data to the `partnerData` object in `main.js`
3. Add partner logos to `assets/img/partners/`
4. Update the trust strip logos on the homepage

### Contact Information
Update contact details in:
- Top bar in all HTML files
- Footer in all HTML files
- Contact page content
- JSON-LD schema in HTML head sections

## 🎭 Animations

The website uses GSAP (GreenSock Animation Platform) for smooth animations:

- **Hero animations**: Staggered text reveals
- **Scroll animations**: Elements animate in as they enter viewport
- **Card hover effects**: Subtle lift and shadow effects
- **Navigation**: Smooth scrolling and navbar behavior
- **Page transitions**: Fade-in effects for content

### Animation Configuration
Animations can be customized in `assets/js/gsap-animations.js`:
- Duration settings
- Easing functions
- Stagger timings
- ScrollTrigger options

## 📱 Responsive Breakpoints

- **xs**: 0px (Extra small devices)
- **sm**: 576px (Small devices)
- **md**: 768px (Medium devices)
- **lg**: 992px (Large devices)
- **xl**: 1200px (Extra large devices)
- **xxl**: 1400px (Extra extra large devices)

## ♿ Accessibility Features

- Semantic HTML5 structure
- ARIA attributes and labels
- Keyboard navigation support
- Focus management for modals
- Skip-to-content links
- High contrast mode support
- Reduced motion support
- Screen reader friendly

## 🔧 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📊 Performance Optimization

- Optimized images with lazy loading
- Minified CSS and JavaScript
- Font preloading
- Efficient animations with GSAP
- Minimal external dependencies
- Compressed assets

## 🚀 Deployment

### Static Hosting
The website can be deployed to any static hosting service:
- Netlify
- Vercel
- GitHub Pages
- AWS S3 + CloudFront
- Traditional web hosting

### Pre-deployment Checklist
- [ ] Test all forms and interactions
- [ ] Verify responsive design on all breakpoints
- [ ] Check accessibility with screen readers
- [ ] Validate HTML and CSS
- [ ] Test performance with Lighthouse
- [ ] Verify all links and images work
- [ ] Test RTL layout for Arabic

## 📞 Support

For technical support or questions about this website:
- Email: <EMAIL>
- Phone: +971 XX XXX XXXX

## 📄 License

© 2024 RIO Middle East Trading LLC. All rights reserved.

---

**Built with ❤️ for RIO Middle East Trading LLC**
